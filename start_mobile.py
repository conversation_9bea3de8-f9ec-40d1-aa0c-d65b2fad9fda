#!/usr/bin/env python3
"""
Mobile-friendly startup script for the Smart QR ID Scanner application
This allows access from mobile devices on the same network
"""

import os
import sys
import socket
import qrcode
from io import BytesIO
import base64

def get_local_ip():
    """Get the local IP address of this machine"""
    try:
        # Connect to a remote address to determine local IP
        s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        s.connect(("*******", 80))
        local_ip = s.getsockname()[0]
        s.close()
        return local_ip
    except Exception:
        return "127.0.0.1"

def generate_qr_code(url):
    """Generate QR code for the URL"""
    qr = qrcode.QRCode(
        version=1,
        error_correction=qrcode.constants.ERROR_CORRECT_L,
        box_size=10,
        border=4,
    )
    qr.add_data(url)
    qr.make(fit=True)
    
    img = qr.make_image(fill_color="black", back_color="white")
    
    # Convert to base64 for display
    buffered = BytesIO()
    img.save(buffered, format="PNG")
    img_str = base64.b64encode(buffered.getvalue()).decode()
    return img_str

def main():
    """Start the mobile-accessible application"""
    print("📱 Starting Smart QR ID Scanner for Mobile Access...")
    print("=" * 60)
    
    # Ensure we're in the right directory
    script_dir = os.path.dirname(os.path.abspath(__file__))
    os.chdir(script_dir)
    
    # Get local IP
    local_ip = get_local_ip()
    port = 5000
    url = f"http://{local_ip}:{port}"
    
    print(f"🌐 Local IP Address: {local_ip}")
    print(f"🔗 Access URL: {url}")
    print()
    
    # Generate QR code for easy mobile access
    try:
        qr_code = generate_qr_code(url)
        print("📱 QR Code for Mobile Access:")
        print("   Scan this QR code with your phone to access the app:")
        print()
        
        # Create a simple QR code in terminal (optional)
        qr = qrcode.QRCode(
            version=1,
            error_correction=qrcode.constants.ERROR_CORRECT_L,
            box_size=1,
            border=1,
        )
        qr.add_data(url)
        qr.make(fit=True)
        qr.print_ascii(invert=True)
        print()
        
    except Exception as e:
        print(f"⚠️  Could not generate QR code: {e}")
    
    print("📋 Mobile Access Instructions:")
    print("   1. Make sure your phone is on the same WiFi network")
    print("   2. Open your phone's browser")
    print("   3. Go to:", url)
    print("   4. Allow camera permissions when prompted")
    print("   5. For best results, use Chrome or Safari")
    print()
    print("🔒 HTTPS Note:")
    print("   Camera access requires HTTPS on most mobile browsers.")
    print("   If you get camera errors, you may need to:")
    print("   - Use localhost on the same device, or")
    print("   - Set up HTTPS with SSL certificates")
    print()
    
    # Import and run the main app
    try:
        from app import app
        
        print("✅ Starting Flask server on all interfaces...")
        print("🚀 Application is starting...")
        print("📧 Email functionality available!")
        print("📱 Mobile camera access configured!")
        print()
        print("Press Ctrl+C to stop the server")
        print("=" * 60)
        
        # Run Flask with network access
        app.run(
            host='0.0.0.0',  # Listen on all interfaces
            port=port,
            debug=False,
            use_reloader=False,
            threaded=True
        )
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("Please ensure all dependencies are installed:")
        print("pip install flask pandas qrcode pillow cryptography reportlab python-dotenv")
    except KeyboardInterrupt:
        print("\n👋 Server stopped by user")
    except Exception as e:
        print(f"❌ Error starting application: {e}")
        print("Please check that all files are present and dependencies are installed.")

if __name__ == "__main__":
    main()
