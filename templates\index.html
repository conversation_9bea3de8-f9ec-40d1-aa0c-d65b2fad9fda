
<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1.0, user-scalable=yes, minimum-scale=1.0, maximum-scale=5.0"
    />
    <title>Smart QR ID Scanner - jLagzn STUDIO</title>
    <link
      rel="icon"
      type="image/png"
      href="{{ url_for('static', filename='trademark/blurbgicon.png') }}"
    />
    <link
      rel="apple-touch-icon"
      href="{{ url_for('static', filename='icons/icon-192.png') }}"
    />
    <meta
      name="description"
      content="Professional QR-based employee ID system with email integration by jLagzn STUDIO"
    />
    <meta name="author" content="jLagzn STUDIO" />
    <meta name="theme-color" content="#8B5CF6" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="default" />
    <meta name="apple-mobile-web-app-title" content="QR ID Scanner" />
    <script src="https://unpkg.com/html5-qrcode"></script>
    <link
      href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css"
      rel="stylesheet"
    />
    <link
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css"
      rel="stylesheet"
    />
    <link
      rel="stylesheet"
      href="{{ url_for('static', filename='css/index.css') }}"
    />
    <link
      rel="stylesheet"
      href="{{ url_for('static', filename='css/button.css') }}"
    />
  </head>
  <body class="bg-light">
    <!-- Modal Overlay (shown on load) -->
    <div id="settingsModal" class="modal-overlay">
      <div id="activatePanel">
        <button class="close-btn" onclick="closeSettingsModal()">
          <i class="fas fa-times"></i>
        </button>

        <div class="modal-content">
          <h4 class="mb-4 text-center">Please Configure Your Settings</h4>

          <div class="mb-4">
            <strong>📄 Current Active Dataset:</strong>
            <div class="d-flex align-items-center gap-2 mt-2">
              <span
                id="current-dataset"
                class="text-truncate"
                style="max-width: 100%"
              >
                {{ active_dataset }}
              </span>
              <button
                class="btn btn-sm btn-outline-primary"
                onclick="triggerFile('dataset')"
              >
                Replace
              </button>
            </div>
            <input
              type="file"
              name="dataset_file"
              accept=".csv"
              class="form-control mt-2 d-none"
              id="datasetInput"
            />
          </div>

          <div class="mb-4">
            <strong>🖼️ Current Active Template:</strong>
            <div class="d-flex align-items-center gap-2 mt-2">
              <span
                id="current-template"
                class="text-truncate"
                style="max-width: 100%"
              >
                {{ active_template }}
              </span>
              <button
                class="btn btn-sm btn-outline-primary"
                onclick="triggerFile('template')"
              >
                Replace
              </button>
            </div>
            <input
              type="file"
              name="template_file"
              accept=".png,.jpg,.jpeg,.webp,.bmp,.gif,.tiff,.tif"
              class="form-control mt-2 d-none"
              id="templateInput"
            />
          </div>

          <div class="mb-4">
            <div class="section-header">
              <i class="fas fa-file-alt me-2"></i>
              <strong>Paper Size</strong>
            </div>
            <div class="paper-size-control mt-2">
              <select id="paperSize" class="form-select">
                <option value="">Please select paper size</option>
                <option value="A4">A4 (210 × 297 mm)</option>
                <option value="Letter">Letter (216 × 279 mm)</option>
                <option value="Legal">Legal (216 × 356 mm)</option>
                <option value="A3">A3 (297 × 420 mm)</option>
                <option value="A5">A5 (148 × 210 mm)</option>
                <option value="A6">A6 (105 × 148 mm)</option>
                <option value="A7">A7 (74 × 100 mm)</option>
                <option value="B5">B5 (182 × 257 mm)</option>
                <option value="B4">B4 (250 × 353 mm)</option>
                <option value="4x6">4 × 6 in (10.16 × 15.24 cm)</option>
                <option value="5x7">5 × 7 in (12.7 × 17.8 cm)</option>
                <option value="5x8">5 × 8 in (12.7 × 20.32 cm)</option>
                <option value="9x13">3.5 × 5 in (8.9 × 12.7 cm)</option>
                <option value="custom">Custom</option>
              </select>
              <div class="paper-size-note">
                Please choose the appropriate paper size for printing
              </div>

              <div
                id="customSizeFields"
                style="display: none; margin-top: 10px"
              >
                <input
                  type="number"
                  id="customWidth"
                  placeholder="Width (in)"
                  step="0.01"
                  class="form-control form-control-sm mb-2"
                  style="max-width: 140px"
                />
                <input
                  type="number"
                  id="customHeight"
                  placeholder="Height (in)"
                  step="0.01"
                  class="form-control form-control-sm"
                  style="max-width: 140px"
                />
              </div>
            </div>
          </div>

          <!-- Email Configuration Section -->
          <div class="mb-4">
            <div class="section-header">
              <i class="fas fa-envelope me-2"></i>
              <strong>Email Configuration</strong>
            </div>
            <div class="email-config-control">
              <div class="row g-2">
                <div class="col-md-6">
                  <input
                    type="email"
                    id="senderEmail"
                    placeholder="Sender Email"
                    class="form-control form-control-sm"
                  />
                </div>
                <div class="col-md-6">
                  <div class="input-group">
                    <input
                      type="password"
                      id="senderPassword"
                      placeholder="App Password"
                      class="form-control form-control-sm"
                    />
                    <button
                      class="btn btn-outline-secondary btn-sm"
                      type="button"
                      onclick="togglePasswordVisibility()"
                      id="passwordToggleBtn"
                      title="Show/Hide Password"
                    >
                      <i class="fas fa-eye" id="passwordToggleIcon"></i>
                    </button>
                  </div>
                </div>
              </div>
              <div class="row g-2 mt-2">
                <div class="col-md-4">
                  <input
                    type="text"
                    id="smtpServer"
                    placeholder="SMTP Server"
                    value="smtp.gmail.com"
                    class="form-control form-control-sm"
                  />
                </div>
                <div class="col-md-4">
                  <input
                    type="number"
                    id="smtpPort"
                    placeholder="Port"
                    value="587"
                    class="form-control form-control-sm"
                  />
                </div>
                <div class="col-md-4">
                  <input
                    type="text"
                    id="senderName"
                    placeholder="Sender Name"
                    value="ID QR System"
                    class="form-control form-control-sm"
                  />
                </div>
              </div>
              <div class="mt-2">
                <button
                  class="btn btn-sm btn-outline-primary"
                  onclick="saveEmailConfig(this)"
                >
                  <i class="fas fa-save me-1"></i> Save Gmail Connection
                </button>
              </div>

              <!-- Email Status Display now uses unified notification system -->
              <!-- Google OAuth2 Section -->
              <div id="googleOAuthSection" class="mb-3" style="display: none;">
                <div class="google-auth-container p-3 border rounded" style="background: linear-gradient(135deg, #4285f4, #34a853);">
                  <div class="text-white text-center">
                    <h6 class="mb-2">
                      <i class="fab fa-google me-2"></i>
                      Sign in with Google (Recommended)
                    </h6>
                    <p class="small mb-3">Easy and secure - no passwords needed!</p>

                    <div id="googleAuthStatus">
                      <button id="googleSignInBtn" class="btn btn-light btn-sm me-2" onclick="signInWithGoogle()">
                        <i class="fab fa-google me-1"></i> Sign in with Google
                      </button>
                      <button id="googleSignOutBtn" class="btn btn-outline-light btn-sm" onclick="signOutGoogle()" style="display: none;">
                        <i class="fas fa-sign-out-alt me-1"></i> Sign out
                      </button>
                    </div>

                    <div id="googleUserInfo" class="mt-2" style="display: none;">
                      <small>
                        <i class="fas fa-user-check me-1"></i>
                        Signed in as: <span id="googleUserName"></span>
                      </small>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Traditional Email Config Section -->
              <div id="traditionalEmailSection">
                <div class="email-config-note">
                  <div class="d-flex align-items-center mb-2">
                    <i class="fab fa-google me-2 text-danger"></i>
                    <strong>Gmail Connection Setup</strong>
                  </div>
                  Configure Gmail settings to send QR codes via email
                  <br><small class="text-warning">
                    <i class="fas fa-info-circle"></i>
                    For Gmail users: Use an <a href="https://support.google.com/accounts/answer/185833" target="_blank" class="text-warning">App Password</a> instead of your regular password
                  </small>
                </div>
              </div>
              <!-- Email configuration status now uses unified notification system -->
            </div>
          </div>

          <!-- Bulk Email Section -->
          {% if email_available %}
          <div class="mb-4">
            <div class="section-header">
              <i class="fas fa-envelope-bulk me-2"></i>
              <strong>📧 Bulk Email All Employees</strong>
            </div>
            <div class="mt-3 p-3 border rounded" style="background: linear-gradient(135deg, #e8f5e8 0%, #d4edda 100%); border-color: #28a745 !important;">
              <p class="small text-muted mb-2">
                <strong>Send QR codes to all employees with email addresses</strong><br>
                Each employee will receive only their own QR code at their registered email address.
              </p>
              <div class="d-flex gap-2 align-items-center">
                <button class="btn btn-sm btn-success" onclick="sendBulkQREmails()" id="bulkEmailBtn">
                  <i class="fas fa-paper-plane me-1"></i> Send All QR Codes
                </button>
                <small class="text-muted">
                  <i class="fas fa-shield-alt me-1"></i>
                  Secure: Each person gets only their own QR code
                </small>
              </div>
              <div id="bulkEmailProgress" class="mt-2" style="display: none;">
                <div class="progress" style="height: 20px;">
                  <div class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" style="width: 0%"></div>
                </div>
                <small class="text-muted mt-1 d-block" id="bulkEmailStatus">Preparing to send emails...</small>
              </div>
            </div>
          </div>
          {% endif %}



          <!-- Download All QR Section -->
          <div class="mb-4">
            <div class="section-header">
              <i class="fas fa-download me-2"></i>
              <strong>📦 Download All QR Codes</strong>
            </div>
            <div class="mt-3 p-3 border rounded" style="background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%); border-color: #ffc107 !important;">
              <p class="small text-muted mb-3">
                <strong>Download all QR codes as a ZIP file</strong><br>
                Includes QR code images with format: qrcode, id, name
              </p>
              <div class="d-flex gap-2 align-items-center">
                <button class="btn btn-sm btn-warning" onclick="downloadAllQRCodes()" id="downloadAllBtn">
                  <i class="fas fa-download me-1"></i> Download All QR Codes
                </button>
                <small class="text-muted">
                  <i class="fas fa-file-archive me-1"></i>
                  Available regardless of email presence
                </small>
              </div>
              <div id="downloadAllProgress" class="mt-2" style="display: none;">
                <div class="progress" style="height: 15px;">
                  <div class="progress-bar progress-bar-striped progress-bar-animated bg-warning" role="progressbar" style="width: 0%"></div>
                </div>
                <small class="text-muted mt-1 d-block" id="downloadAllStatus">Preparing download...</small>
              </div>
            </div>
          </div>

          <div class="d-flex justify-content-end">
            <button class="btn btn-primary" onclick="submitSettings()">
              <i class="fas fa-check me-1"></i> Confirm Settings
            </button>
          </div>
        </div>
      </div>
    </div>

      <div class="container-fluid px-2 px-md-4 py-3 py-md-5">
        <!-- Server-side success messages now use unified notification system -->

        <!-- Header with Logo -->
        <div class="d-flex justify-content-between align-items-center mb-4">
          <div class="d-flex align-items-center gap-3">
            <img
              src="{{ url_for('static', filename='trademark/blurbgicon.png') }}"
              alt="jLagzn STUDIO Logo"
              style="
                width: 50px;
                height: 50px;
                border-radius: 50%;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
              "
            />
            <div>
              <h2
                class="mb-0"
                style="
                  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                  -webkit-background-clip: text;
                  -webkit-text-fill-color: transparent;
                  background-clip: text;
                  font-weight: 700;
                "
              >
                Smart QR ID Scanner
              </h2>
              <small class="text-muted">by jLagzn STUDIO</small>
            </div>
          </div>

          <button class="settingbtn" onclick="openSettingsModal()">
            <div id="container-stars">
              <div id="stars"></div>
            </div>
            <div id="glow">
              <div class="circle"></div>
              <div class="circle"></div>
            </div>
            <strong><i class="fas fa-cog"></i> Settings</strong>
          </button>


        </div>

        <!-- Main Panel -->
        <div class="card shadow-lg border-0 rounded p-2 p-md-4">
          <div
            class="d-flex flex-column flex-lg-row justify-content-between gap-3 gap-lg-4"
          >
            <!-- ID Template Preview (left side) -->
            <div
              class="preview-section flex-grow-1 mb-3 mb-lg-0"
              style="flex-basis: 0; min-width: 0; max-width: 100%"
            >
              <h5 class="section-title mb-2">ID Template Preview</h5>
              <div id="paperBoundary" class="d-flex align-items-center
              justify-content-center" style="background-image: url('{{
              url_for("static", filename="id_templates/" + active_template)
              }}')"> >
              <div
                id="scaledPreviewWrapper"
                style="transform: scale(1); transform-origin: center center"
              >
                <div
                  id="idPreviewContainer"
                  style="
                    position: relative;
                    background: white;
                    box-sizing: content-box;
                  "
                >
                  <img
                    id="id-template-preview"
                    src="{{ url_for('static', filename='id_templates/' + active_template) }}?v={{ config['active_dataset'] }}"
                    alt="ID Template Preview"
                    style="
                      width: 100%;
                      height: 100%;
                      object-fit: contain;
                      background-color: white;
                      display: block;
                      z-index: 1;
                      position: relative;
                    "
                  />
                  <div
                    class="position-absolute id-overlay"
                    style="
                      inset: 0;
                      display: flex;
                      flex-direction: column;
                      justify-content: center;
                      align-items: center;
                      pointer-events: none;
                      z-index: 2;
                      color: black;
                      text-align: center;
                    "
                  >
                    <div id="preview-id" style="font-weight: bold">000</div>
                    <div style="height: 1rem"></div>
                    <div id="preview-name" style="font-weight: bold">
                      Name Here
                    </div>
                    <div id="preview-position">Position</div>
                    <div id="preview-company" style="font-weight: bold">
                      Company
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Scanner and ID Info (right side) -->
          <div
            class="scanner-section flex-grow-1"
            style="flex-basis: 0; min-width: 0; max-width: min(500px, 100%)"
          >
            <div class="form-check text-start mb-3">
              <input
                class="form-check-input"
                type="checkbox"
                id="previewBeforePrint"
              />
              <label class="form-check-label" for="previewBeforePrint">
                Show print preview before printing
              </label>
            </div>
            <h5 class="section-title mb-2">Smart QR ID Scanner</h5>

            <!-- Alert box now uses unified notification system -->
            <div class="text-center mb-2">
              <div
                class="spinner-border text-light d-none"
                id="loadingSpinner"
              ></div>
            </div>

            <div class="d-flex justify-content-center mb-3">
              <div
                id="reader"
                class="border rounded shadow-sm bg-white"
                style="
                  width: 100%;
                  max-width: min(400px, 90vw);
                  height: auto;
                  aspect-ratio: 4 / 3;
                  min-height: 240px;
                "
              ></div>
            </div>

            <!-- Camera Controls -->
            <div class="d-flex flex-wrap gap-2 justify-content-center mb-3">
              <button
                id="start-button"
                class="btn btn-success btn-sm"
                onclick="startScanning()"
              >
                <i class="fas fa-camera me-1"></i> Start Camera
              </button>
              <button
                id="stop-button"
                class="btn btn-danger btn-sm"
                onclick="stopScanning()"
                style="display: none"
              >
                <i class="fas fa-stop me-1"></i> Stop Camera
              </button>
              <button
                id="switch-camera"
                class="btn btn-outline-primary btn-sm"
                onclick="switchCamera()"
                style="display: none"
              >
                <i class="fas fa-sync-alt me-1"></i> Switch Camera
              </button>
            </div>

            <!-- Email Employee Selection (Enhanced) -->
            <div class="mt-3 p-4 border rounded" style="background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%); border-color: #6f42c1 !important;">
              <div class="d-flex align-items-center justify-content-between mb-3">
                <h6 class="mb-0" style="color: #6f42c1;">
                  <i class="fas fa-envelope me-2"></i>Send QR Code via Email
                </h6>
                <small class="text-muted">
                  <i class="fas fa-magic me-1"></i>Auto-lookup enabled
                </small>
              </div>

              <!-- Employee ID Input with Auto-lookup -->
              <div class="row g-3 mb-3">
                <div class="col-md-4">
                  <label class="form-label small fw-bold text-muted">Employee ID</label>
                  <input
                    type="number"
                    id="manualEmployeeIdMain"
                    placeholder="Enter ID (e.g., 1, 2, 3)"
                    class="form-control"
                    style="border-color: #6f42c1; border-width: 2px;"
                    min="1"
                    oninput="autoLookupEmployee()"
                  >
                  <div class="form-text">
                    <i class="fas fa-search me-1"></i>Auto-retrieves from active CSV
                  </div>
                </div>
                <div class="col-md-5">
                  <label class="form-label small fw-bold text-muted">Email Address</label>
                  <input
                    type="email"
                    id="recipientEmailMain"
                    placeholder="Email will auto-populate"
                    class="form-control"
                    style="border-color: #6f42c1; border-width: 2px;"
                    oninput="validateEmailInput()"
                  >
                  <div class="form-text">
                    <i class="fas fa-edit me-1"></i>Editable after auto-fill
                  </div>
                </div>
                <div class="col-md-3 d-flex align-items-end">
                  <button class="btn w-100" style="background: #6f42c1; color: white; border-color: #6f42c1; border-width: 2px;" onclick="sendQREmailMain()" id="sendEmailMainBtn" disabled>
                    <i class="fas fa-paper-plane me-1"></i> Send QR
                  </button>
                </div>
              </div>

              <!-- Employee Info Display -->
              <div id="employeeInfoMain" class="p-3 border rounded bg-white shadow-sm" style="display: none;">
                <div class="d-flex align-items-center justify-content-between">
                  <div>
                    <small class="text-muted d-block mb-1">
                      <i class="fas fa-user me-1"></i>Selected Employee:
                    </small>
                    <span id="employeeNameMain" class="fw-bold text-primary"></span>
                  </div>
                  <div class="text-end">
                    <small class="text-muted d-block mb-1">Position:</small>
                    <span id="employeePositionMain" class="text-muted small"></span>
                  </div>
                </div>
              </div>

              <!-- Progress Indicator -->
              <div id="emailProgressMain" class="mt-3" style="display: none;">
                <div class="progress" style="height: 20px;">
                  <div class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" style="width: 0%; background-color: #6f42c1;"></div>
                </div>
                <small class="text-muted mt-1 d-block" id="emailStatusMain">Preparing to send email...</small>
              </div>
            </div>



            <div class="mt-4">
              <h5 class="section-title mb-2">ID Info</h5>
              <div class="border rounded p-3 bg-white shadow-sm">
                <p><strong>ID:</strong> <span id="id"></span></p>
                <p><strong>Name:</strong> <span id="name"></span></p>
                <p><strong>Position:</strong> <span id="position"></span></p>
                <p><strong>Company:</strong> <span id="company"></span></p>

                <!-- Email Sending Section -->
                {% if email_available %}
                <div
                  class="mt-3 pt-3 border-top"
                  id="emailSection"
                  style="display: none"
                >
                  <div class="d-flex align-items-center gap-2">
                    <input
                      type="email"
                      id="recipientEmail"
                      placeholder="Enter email to send QR code"
                      class="form-control form-control-sm"
                    />
                    <button
                      class="btn btn-sm btn-success"
                      onclick="sendQREmail()"
                      id="sendEmailBtn"
                    >
                      <i class="fas fa-envelope me-1"></i> Send QR
                    </button>
                  </div>
                  <div class="form-text">
                    <i class="fas fa-info-circle me-1"></i>
                    Send the QR code to the employee's email address
                  </div>


                </div>
                {% else %}
                <div
                  class="mt-3 pt-3 border-top"
                  id="noEmailSection"
                  style="display: none"
                >
                  <div class="alert alert-info mb-3">
                    <i class="fas fa-info-circle me-2"></i>
                    <strong>Email feature not available:</strong>
                    Upload a CSV file with an "Email" column to enable email
                    sending functionality.
                  </div>
                  <div class="text-center">
                    <button
                      type="button"
                      class="btn btn-success btn-lg"
                      onclick="downloadQRCode()"
                      id="downloadQRBtn"
                    >
                      <i class="fas fa-download me-2"></i>Download QR Code
                    </button>
                    <div class="text-muted mt-2">
                      <small
                        >Download the QR code image for this employee</small
                      >
                    </div>
                  </div>
                </div>
                {% endif %}
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Footer -->
      <div
        class="text-center mt-4 py-3"
        style="border-top: 1px solid rgba(255, 255, 255, 0.2)"
      >
        <div class="d-flex align-items-center justify-content-center gap-2">
          <img
            src="{{ url_for('static', filename='trademark/blurbgicon.png') }}"
            alt="jLagzn STUDIO Logo"
            style="width: 25px; height: 25px; border-radius: 50%"
          />
          <small class="text-muted">
            ID QR System v2.0 © 2025 jLagzn STUDIO | All rights reserved
          </small>
        </div>
      </div>
    </div>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>
    <script>
      console.log("JavaScript is loading..."); // Debug test

      // Global variables
      // alertBox removed - now using unified notification system
      const spinner = document.getElementById("loadingSpinner");
      let html5QrCode = null;
      let scannerActive = false;
      let datasetFile = null;
      let templateFile = null;
      let settingsConfigured = localStorage.getItem('settingsConfigured') === 'true';
      let currentEmployeeId = null;
      let currentEmployeeEmail = null;
      let emailAvailable = {{ 'true' if email_available else 'false' }};
      let currentCameraId = null;
      let availableCameras = [];

      const paperSizeMap = {
        A4: [8.27, 11.69],
        Letter: [8.5, 11],
        Legal: [8.5, 14],
        A3: [11.7, 16.5],
        A5: [5.8, 8.3],
        A6: [4.1, 5.8],
        A7: [2.9, 3.9],
        B5: [7.2, 10.1],
        B4: [9.8, 13.9],
        "4x6": [4, 6],
        "5x7": [5, 7],
        "5x8": [5, 8],
        "9x13": [3.5, 5]
      };

      // Initialize on DOM load
      document.addEventListener("DOMContentLoaded", function() {
        // Check if we're coming from first run setup
        const urlParams = new URLSearchParams(window.location.search);
        const fromSetup = urlParams.get('from_setup') === 'true';

        // Show settings modal on first load only if NOT coming from first run
        if (!settingsConfigured && !fromSetup) {
          document.getElementById("settingsModal").style.display = "flex";
        } else if (fromSetup) {
          // If coming from first run, mark settings as configured to avoid modal
          settingsConfigured = true;
          localStorage.setItem('settingsConfigured', 'true');
        }

        // Show success alert if needed using unified notification system
        {% if success %}
        // Only show success message if not coming from first run setup to avoid modal conflicts
        const fromSetup = urlParams.get('from_setup') === 'true';
        if (!fromSetup) {
          showAlert("✅ Activation successful. Dataset and/or template updated.", "success");
        }
        {% endif %}

        // Set up paper size controls
        setupPaperSizeControls();
      });

      // Test function to verify JavaScript is working
      function testJS() {
        alert("JavaScript is working!");
        console.log("Test function called successfully");
      }

      // Modal functions
      function openSettingsModal() {
        console.log("openSettingsModal called");
        document.getElementById("settingsModal").style.display = "flex";
      }

      function closeSettingsModal() {
        console.log("closeSettingsModal called"); // Debug log
        const modal = document.getElementById("settingsModal");
        if (modal) {
          modal.style.display = "none";
          console.log("Modal closed successfully"); // Debug log

          // If settings are configured, show success message
          if (settingsConfigured) {
            showAlert("✅ Settings modal closed. You can reopen it anytime from the settings.", "info");
          } else {
            showAlert("⚠️ Settings modal closed. Please configure settings to use the scanner.", "warning");
          }
        } else {
          console.error("Modal element not found"); // Debug log
        }
      }

      function resetSettings() {
        localStorage.removeItem('settingsConfigured');
        settingsConfigured = false;
        location.reload();
      }

      function submitSettings() {
        console.log("submitSettings called"); // Debug log

        const paperSize = document.getElementById("paperSize").value;
        console.log("Selected paper size:", paperSize); // Debug log

        if (!paperSize) {
          console.log("No paper size selected"); // Debug log
          showAlert("⚠️ Please select a paper size before continuing", "warning");
          return;
        }

        // Show loading feedback
        const submitBtn = document.querySelector('button[onclick="submitSettings()"]');
        if (submitBtn) {
          const originalText = submitBtn.innerHTML;
          submitBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-1" role="status"></span> Configuring...';
          submitBtn.disabled = true;

          setTimeout(() => {
            submitBtn.innerHTML = originalText;
            submitBtn.disabled = false;
          }, 1000);
        }

        showAlert("⚙️ Configuring settings...", "info");

        console.log("Settings configured, closing modal and starting camera"); // Debug log
        settingsConfigured = true;
        localStorage.setItem('settingsConfigured', 'true');
        localStorage.setItem('paperSize', paperSize);

        setTimeout(() => {
          closeSettingsModal();
          showAlert("✅ Settings configured successfully! Camera is ready for scanning.", "success");

          // Add a small delay to ensure modal is closed before starting camera
          setTimeout(() => {
            initCamera();
          }, 300);
        }, 800);
      }

      // Camera functions
      function initCamera() {
        console.log("initCamera called"); // Debug log

        if (html5QrCode) {
          console.log("Camera already initialized"); // Debug log
          return;
        }

        try {
          html5QrCode = new Html5Qrcode("reader");
          startCamera();
        } catch (error) {
          console.error("Error initializing camera:", error);
          showAlert("❌ Error initializing camera: " + error.message, "danger");
        }
      }

      function startCamera() {
        if (scannerActive || !settingsConfigured) return;

        scannerActive = true;
        html5QrCode.start(
          { facingMode: "environment" },
          { fps: 10, qrbox: 250 },
          (decodedText) => {
            scannerActive = false;
            html5QrCode.stop().then(() => {
              fetchData(decodedText.trim());
            });
          },
          (error) => {
            scannerActive = false;
            console.error("QR Scanner error:", error);
          }
        ).catch((err) => {
          scannerActive = false;
          showAlert("Camera error: " + err.message, "danger");
        });
      }

      // Data handling
      function clearFields() {
        ["id", "name", "position", "company"].forEach(
          (id) => (document.getElementById(id).textContent = "")
        );
        // Hide email sections when clearing fields
        if (emailAvailable) {
          document.getElementById("emailSection").style.display = "none";
        } else {
          const noEmailSection = document.getElementById("noEmailSection");
          if (noEmailSection) {
            noEmailSection.style.display = "none";
          }
        }
        currentEmployeeId = null;
        currentEmployeeEmail = null;
      }

      function fetchData(qrContent) {
        clearFields();
        spinner.classList.remove("d-none");

        fetch("/get_data", {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({ qr_content: qrContent }),
        })
          .then((res) => res.json())
          .then((data) => {
            spinner.classList.add("d-none");

            if (data.error) {
              showAlert("❌ Record not found or invalid QR code", "danger");
              resetSystem();
              return;
            }

            // Update display fields
            ["ID", "Name", "Position", "Company"].forEach((field) => {
              document.getElementById(field.toLowerCase()).textContent = data[field];
            });

            // Store current employee ID and email for functionality
            currentEmployeeId = data["ID"];
            currentEmployeeEmail = data["Email"] || null;

            // Update preview
            document.getElementById("preview-id").textContent = String(data["ID"]).padStart(3, "0");
            document.getElementById("preview-name").textContent = data["Name"];
            document.getElementById("preview-position").textContent = data["Position"];
            document.getElementById("preview-company").textContent = data["Company"];

            // Show appropriate email section
            if (emailAvailable) {
              document.getElementById("emailSection").style.display = "block";

              // Auto-populate email if available
              const emailInput = document.getElementById("recipientEmail");
              if (currentEmployeeEmail) {
                emailInput.value = currentEmployeeEmail;
                emailInput.placeholder = "Email auto-populated from dataset";
              } else {
                emailInput.value = "";
                emailInput.placeholder = "Enter email to send QR code";
              }
            } else {
              const noEmailSection = document.getElementById("noEmailSection");
              if (noEmailSection) {
                noEmailSection.style.display = "block";
              }
            }

            // Auto print
            printAndRestart();
          })
          .catch((err) => {
            spinner.classList.add("d-none");
            showAlert("Fetch error: " + err.message, "danger");
            resetSystem();
          });
      }

      // Printing functions
      function printAndRestart() {
        const preview = document.getElementById("idPreviewContainer");
        const wrapper = document.getElementById("scaledPreviewWrapper");
        const paperSize = document.getElementById("paperSize").value;
        const customWidth = parseFloat(document.getElementById("customWidth").value) || 3.375;
        const customHeight = parseFloat(document.getElementById("customHeight").value) || 2.125;
        const showPreview = document.getElementById("previewBeforePrint").checked;

        const TARGET_DPI = 300;
        const SCREEN_PPI = 96;
        const scale = TARGET_DPI / SCREEN_PPI;

        const originalTransform = wrapper.style.transform;
        wrapper.style.transform = "scale(1)";

        html2canvas(preview, {
          scale: scale,
          useCORS: true,
          allowTaint: true,
          backgroundColor: null,
        }).then((canvas) => {
          wrapper.style.transform = originalTransform;

          const base64Image = canvas.toDataURL("image/png");

          const payload = {
            image_base64: base64Image,
            paper_size: paperSize,
            custom_width: customWidth,
            custom_height: customHeight,
          };

          if (paperSize !== "custom") {
            delete payload.custom_width;
            delete payload.custom_height;
          }

          if (showPreview) {
            payload.redirect = true;
          }

          fetch("/print_image_direct", {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify(payload),
          })
            .then((res) => res.json())
            .then((result) => {
              if (showPreview && result.success && result.preview_url) {
                window.location.href = result.preview_url;
              } else if (!showPreview && result.success) {
                setTimeout(() => resetSystem(), 3000);
              } else {
                showAlert("❌ Failed to print: " + (result.error || "Unknown error"), "danger");
                resetSystem();
              }
            })
            .catch((err) => {
              showAlert("❌ Network error: " + err.message, "danger");
              resetSystem();
            });
        });
      }

      function resetSystem() {
        clearFields();
        startCamera();
      }

      // Settings panel functions
      function triggerFile(type) {
        document.getElementById(`${type}Input`).click();
      }

      function submitActivation() {
        if (!datasetFile && !templateFile) {
          showAlert("Please select at least a dataset or template file.", "warning");
          return;
        }

        const formData = new FormData();
        if (datasetFile) formData.append("dataset_file", datasetFile);
        if (templateFile) formData.append("template_file", templateFile);
        formData.append("triggeredBy", "manual");

        fetch("/activate", {
          method: "POST",
          body: formData,
        })
          .then((res) => {
            if (res.redirected) {
              window.location.href = res.url;
            } else {
              showAlert("Activation failed.", "danger");
            }
          })
          .catch((err) => {
            showAlert("Error during activation: " + err.message, "danger");
          });
      }

      // Paper size functions
      function setupPaperSizeControls() {
        const paperSelect = document.getElementById("paperSize");
        const customFields = document.getElementById("customSizeFields");

        paperSelect.addEventListener("change", function() {
          if (this.value === "custom") {
            customFields.style.display = "block";
            return;
          }
          customFields.style.display = "none";
          setPaperSize(this.value);
        });

        document.getElementById("customWidth").addEventListener("input", applyCustomSize);
        document.getElementById("customHeight").addEventListener("input", applyCustomSize);

        // Set default size if one is already selected
        if (paperSelect.value) {
          setPaperSize(paperSelect.value);
        }
      }

      function setPaperSize(size) {
        if (size === "custom") return;

        const [width, height] = paperSizeMap[size] || paperSizeMap["A4"];
        updatePreviewSize(width, height);
      }

      function applyCustomSize() {
        const width = parseFloat(document.getElementById("customWidth").value);
        const height = parseFloat(document.getElementById("customHeight").value);

        if (width && height) {
          updatePreviewSize(width, height);
        }
      }

      function updatePreviewSize(widthInInches, heightInInches) {
        const DPI = 96;
        const MARGIN_MM = 1;
        const MARGIN_IN = MARGIN_MM / 25.4;
        const maxFramePx = 500;
        const paddingPercent = 0.05;

        const widthPx = (widthInInches - 2 * MARGIN_IN) * DPI;
        const heightPx = (heightInInches - 2 * MARGIN_IN) * DPI;

        const availableWidth = maxFramePx * (1 - 2 * paddingPercent);
        const availableHeight = maxFramePx * (1 - 2 * paddingPercent);
        const scale = Math.min(availableWidth / widthPx, availableHeight / heightPx);

        const preview = document.getElementById("idPreviewContainer");
        preview.style.width = `${widthPx}px`;
        preview.style.height = `${heightPx}px`;

        const wrapper = document.getElementById("scaledPreviewWrapper");
        wrapper.style.transform = `scale(${scale})`;

        // Font scaling
        const baseFontSizePx = 16;
        const referenceHeightInches = 5.8;
        const fontScale = heightInInches / referenceHeightInches;

        document.getElementById("preview-id").style.fontSize = `${Math.round(baseFontSizePx * 1.8 * fontScale)}px`;
        document.getElementById("preview-name").style.fontSize = `${Math.round(baseFontSizePx * 1.4 * fontScale)}px`;
        document.getElementById("preview-position").style.fontSize = `${Math.round(baseFontSizePx * 1.1 * fontScale)}px`;
        document.getElementById("preview-company").style.fontSize = `${Math.round(baseFontSizePx * 1.3 * fontScale)}px`;
      }

      // Helper functions
      // Unified notification system - only one notification at a time
      let currentNotification = null;

      function showAlert(message, type = "info") {
        console.log("showAlert called:", message, type); // Debug log

        // Remove any existing notification first to prevent overlapping
        if (currentNotification && currentNotification.parentNode) {
          currentNotification.remove();
          currentNotification = null;
        }

        // Create a single floating alert in upper right
        const floatingAlert = document.createElement('div');
        floatingAlert.className = `alert alert-${type} position-fixed`;
        floatingAlert.style.cssText = `
          top: 20px;
          right: 20px;
          z-index: 9999;
          min-width: 300px;
          max-width: 400px;
          box-shadow: 0 4px 12px rgba(0,0,0,0.3);
          border: none;
          border-radius: 8px;
        `;
        floatingAlert.innerHTML = `
          <div class="d-flex align-items-center">
            <span class="me-2">${message}</span>
            <button type="button" class="btn-close ms-auto" onclick="this.parentElement.parentElement.remove(); currentNotification = null;"></button>
          </div>
        `;

        document.body.appendChild(floatingAlert);
        currentNotification = floatingAlert;

        // Auto-remove after 5 seconds
        setTimeout(() => {
          if (floatingAlert.parentNode) {
            floatingAlert.remove();
            if (currentNotification === floatingAlert) {
              currentNotification = null;
            }
          }
        }, 5000);
      }

      // File input handlers
      document.getElementById("datasetInput").addEventListener("change", function(e) {
        datasetFile = e.target.files[0];
        document.getElementById("current-dataset").textContent = datasetFile.name;
      });

      document.getElementById("templateInput").addEventListener("change", function(e) {
        templateFile = e.target.files[0];
        document.getElementById("current-template").textContent = templateFile.name;
      });

      // Initialize Google OAuth2 status on page load
      checkGoogleAuthStatus();

      // Initialize email status
      showEmailStatus("Configure Gmail connection to send QR codes", "info");

      // Google OAuth2 Functions
      async function checkGoogleAuthStatus() {
        try {
          const response = await fetch("/google_auth_status");
          const data = await response.json();

          if (data.available && data.configured) {
            document.getElementById("googleOAuthSection").style.display = "block";

            if (data.authenticated) {
              document.getElementById("googleSignInBtn").style.display = "none";
              document.getElementById("googleSignOutBtn").style.display = "inline-block";
              document.getElementById("googleUserInfo").style.display = "block";
              document.getElementById("googleUserName").textContent = `${data.user_name} (${data.user_email})`;

              // Hide traditional email config when OAuth is active
              document.getElementById("traditionalEmailSection").style.display = "none";
            } else {
              document.getElementById("googleSignInBtn").style.display = "inline-block";
              document.getElementById("googleSignOutBtn").style.display = "none";
              document.getElementById("googleUserInfo").style.display = "none";

              // Show traditional email config
              document.getElementById("traditionalEmailSection").style.display = "block";
            }
          } else {
            // OAuth not available/configured, show traditional config
            document.getElementById("googleOAuthSection").style.display = "none";
            document.getElementById("traditionalEmailSection").style.display = "block";

            if (!data.available) {
              console.log("Google APIs not available");
            } else if (!data.configured) {
              console.log("Google OAuth2 not configured");
            }
          }
        } catch (error) {
          console.error("Error checking Google auth status:", error);
          // Fallback to traditional email config
          document.getElementById("googleOAuthSection").style.display = "none";
          document.getElementById("traditionalEmailSection").style.display = "block";
        }
      }

      async function signInWithGoogle() {
        try {
          const response = await fetch("/google_auth_url");
          const data = await response.json();

          if (data.auth_url) {
            // Open Google OAuth2 in a popup
            const popup = window.open(
              data.auth_url,
              'google_oauth',
              'width=500,height=600,scrollbars=yes,resizable=yes'
            );

            // Check if popup is closed (user completed auth)
            const checkClosed = setInterval(() => {
              if (popup.closed) {
                clearInterval(checkClosed);
                // Refresh auth status
                setTimeout(() => {
                  checkGoogleAuthStatus();
                  showAlert("✅ Successfully signed in with Google!", "success");
                }, 1000);
              }
            }, 1000);

          } else {
            showAlert("❌ Failed to get Google authorization URL", "danger");
          }
        } catch (error) {
          console.error("Error signing in with Google:", error);
          showAlert("❌ Error signing in with Google: " + error.message, "danger");
        }
      }

      async function signOutGoogle() {
        try {
          const response = await fetch("/google_logout", {
            method: "POST",
            headers: {"Content-Type": "application/json"}
          });

          const data = await response.json();

          if (data.success) {
            showAlert("✅ Successfully signed out from Google", "success");
            checkGoogleAuthStatus();
          } else {
            showAlert("❌ Failed to sign out: " + (data.error || "Unknown error"), "danger");
          }
        } catch (error) {
          console.error("Error signing out from Google:", error);
          showAlert("❌ Error signing out: " + error.message, "danger");
        }
      }

      // Email Status Display Functions - now uses unified notification system
      function showEmailStatus(message, status) {
        // Map status to alert type and add appropriate icon
        let alertType = "info";
        let icon = "📧";

        switch(status) {
          case "connecting":
            alertType = "info";
            icon = "🔗";
            break;
          case "connected":
            alertType = "success";
            icon = "✅";
            break;
          case "sending":
            alertType = "warning";
            icon = "📤";
            break;
          case "sent":
            alertType = "success";
            icon = "✅";
            break;
          case "error":
            alertType = "danger";
            icon = "❌";
            break;
        }

        // Use unified notification system
        showAlert(`${icon} ${message}`, alertType);
      }

      // Email Configuration Functions
      function saveEmailConfig(btn) {
        console.log("saveEmailConfig called"); // Debug log
        const originalText = btn.innerHTML;

        const config = {
          smtp_server: document.getElementById("smtpServer").value || "smtp.gmail.com",
          smtp_port: parseInt(document.getElementById("smtpPort").value) || 587,
          use_tls: true,
          sender_email: document.getElementById("senderEmail").value,
          sender_password: document.getElementById("senderPassword").value,
          sender_name: document.getElementById("senderName").value || "QR System",
        };

        console.log("Saving email config:", config); // Debug log

        // Validate required fields
        if (!config.sender_email || !config.sender_password) {
          showAlert("⚠️ Please enter sender email and password", "warning");
          return;
        }

        if (!config.sender_email.includes("@")) {
          showAlert("⚠️ Please enter a valid email address", "warning");
          return;
        }

        if (isNaN(config.smtp_port) || config.smtp_port < 1 || config.smtp_port > 65535) {
          showAlert("⚠️ Please enter a valid SMTP port (1-65535)", "warning");
          return;
        }

        btn.innerHTML = '<span class="spinner-border spinner-border-sm me-1" role="status"></span> Connecting...';
        btn.disabled = true;

        showAlert("🔗 Connecting to Gmail server...", "info");

        fetch("/update_email_config", {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify(config),
        })
          .then((res) => {
            console.log("Save config response status:", res.status);
            if (!res.ok) {
              throw new Error(`HTTP ${res.status}: ${res.statusText}`);
            }
            return res.json();
          })
          .then((data) => {
            console.log("Save config response:", data);
            if (data.success) {
              showAlert("✅ Gmail connection established successfully! Ready to send QR codes via email.", "success");
            } else {
              showAlert("❌ Gmail connection failed: " + (data.error || "Unknown error"), "danger");
            }
          })
          .catch((err) => {
            console.error("Gmail connection error:", err);
            showAlert("❌ Gmail connection error: " + err.message, "danger");
          })
          .finally(() => {
            btn.innerHTML = originalText;
            btn.disabled = false;
          });
      }

      async function sendBulkQREmails() {
        const btn = document.getElementById("bulkEmailBtn");
        const progressDiv = document.getElementById("bulkEmailProgress");
        const progressBar = progressDiv.querySelector(".progress-bar");
        const statusText = document.getElementById("bulkEmailStatus");

        // Confirm action
        if (!confirm("Are you sure you want to send QR codes to all employees with email addresses?\n\nEach employee will receive only their own QR code.")) {
          return;
        }

        const originalText = btn.innerHTML;
        btn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status"></span> Sending...';
        btn.disabled = true;
        progressDiv.style.display = "block";
        progressBar.style.width = "10%";
        statusText.textContent = "Starting bulk email send...";

        showAlert("📤 Starting bulk email send to all employees...", "info");

        try {
          progressBar.style.width = "30%";
          statusText.textContent = "Sending emails to all employees...";

          const response = await fetch("/send_bulk_qr_emails", {
            method: "POST",
            headers: { "Content-Type": "application/json" },
          });

          const data = await response.json();

          progressBar.style.width = "100%";

          if (data.success) {
            const summary = data.summary;
            statusText.textContent = `Completed: ${summary.successful_sends}/${summary.total_employees} emails sent successfully`;

            showAlert(
              `✅ Bulk email completed!\n\n` +
              `📊 Summary:\n` +
              `• Total employees with emails: ${summary.total_employees}\n` +
              `• Successfully sent: ${summary.successful_sends}\n` +
              `• Failed: ${summary.failed_sends}\n\n` +
              `Each employee received only their own QR code.`,
              "success"
            );

            // Show detailed results if there were failures
            if (summary.failed_sends > 0) {
              console.log("Failed email sends:", summary.results.filter(r => !r.success));
            }
          } else {
            statusText.textContent = "Bulk email failed";
            showAlert(`❌ Bulk email failed: ${data.error}`, "danger");

            if (data.summary) {
              console.log("Bulk email summary:", data.summary);
            }
          }
        } catch (error) {
          progressBar.style.width = "100%";
          progressBar.classList.add("bg-danger");
          statusText.textContent = "Error occurred during bulk email send";
          showAlert(`❌ Error sending bulk emails: ${error.message}`, "danger");
          console.error("Bulk email error:", error);
        } finally {
          btn.innerHTML = originalText;
          btn.disabled = false;

          // Hide progress after 5 seconds
          setTimeout(() => {
            progressDiv.style.display = "none";
            progressBar.style.width = "0%";
            progressBar.classList.remove("bg-danger");
          }, 5000);
        }
      }



      async function downloadAllQRCodes() {
        const btn = document.getElementById("downloadAllBtn");
        const progressDiv = document.getElementById("downloadAllProgress");
        const progressBar = progressDiv.querySelector(".progress-bar");
        const statusText = document.getElementById("downloadAllStatus");

        const originalText = btn.innerHTML;
        btn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status"></span> Preparing...';
        btn.disabled = true;
        progressDiv.style.display = "block";
        progressBar.style.width = "10%";
        statusText.textContent = "Preparing download...";

        showAlert("📦 Preparing to download all QR codes...", "info");

        try {
          progressBar.style.width = "50%";
          statusText.textContent = "Creating ZIP file...";

          const response = await fetch("/download_all_qr", {
            method: "POST",
            headers: { "Content-Type": "application/json" }
          });

          if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
          }

          progressBar.style.width = "90%";
          statusText.textContent = "Starting download...";

          // Get the blob and create download link
          const blob = await response.blob();
          const url = window.URL.createObjectURL(blob);
          const link = document.createElement('a');
          link.href = url;
          link.download = 'all_qr_codes.zip';
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
          window.URL.revokeObjectURL(url);

          progressBar.style.width = "100%";
          statusText.textContent = "Download completed!";
          showAlert("✅ All QR codes downloaded successfully!", "success");

        } catch (error) {
          progressBar.style.width = "100%";
          progressBar.classList.add("bg-danger");
          statusText.textContent = "Download failed";
          showAlert(`❌ Error downloading QR codes: ${error.message}`, "danger");
          console.error("Download all QR error:", error);
        } finally {
          btn.innerHTML = originalText;
          btn.disabled = false;

          // Hide progress after 3 seconds
          setTimeout(() => {
            progressDiv.style.display = "none";
            progressBar.style.width = "0%";
            progressBar.classList.remove("bg-danger");
          }, 3000);
        }
      }

      async function sendQREmail() {
        const email = document.getElementById("recipientEmail").value.trim();
        const btn = document.getElementById("sendEmailBtn");

        if (!email) {
          showAlert("Please enter an email address", "warning");
          document.getElementById("recipientEmail").focus();
          return;
        }

        // Basic email validation
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(email)) {
          showAlert("Please enter a valid email address", "warning");
          document.getElementById("recipientEmail").focus();
          return;
        }

        if (!currentEmployeeId) {
          showAlert("No employee selected", "warning");
          return;
        }

        const originalText = btn.innerHTML;
        btn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status"></span> Sending...';
        btn.disabled = true;

        // Show sending status with detailed progress
        showAlert("📤 Preparing to send QR code email...", "info");
        showEmailStatus("Preparing email...", "sending");
        showEmailStatus(`Sending QR code to ${email}...`, "sending");

        try {
          // Check if Google OAuth2 is available and authenticated
          const authResponse = await fetch("/google_auth_status");
          const authData = await authResponse.json();

          let endpoint = "/send_qr_email"; // Default SMTP endpoint
          let method = "POST";
          let body = {
            employee_id: currentEmployeeId,
            email: email,
          };

          // Use OAuth2 if available and authenticated
          if (authData.available && authData.authenticated) {
            endpoint = "/send_email_oauth";
            body.recipient_email = email;
            body.recipient_name = email.split('@')[0]; // Use email prefix as name
            showAlert("📤 Sending via Google OAuth2...", "info");
            showEmailStatus("Sending via Google OAuth2...", "sending");
          } else {
            showAlert("📤 Sending via Gmail SMTP...", "info");
            showEmailStatus("Sending via Gmail SMTP...", "sending");
          }

          const response = await fetch(endpoint, {
            method: method,
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify(body),
          });

          console.log("Send email response status:", response.status);

          if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
          }

          const data = await response.json();
          console.log("Send email response:", data);

          if (data.success) {
            const method = authData.available && authData.authenticated ? "Google OAuth2" : "Gmail SMTP";
            showAlert(`✅ QR code sent successfully to ${email} via ${method}! Check your email for the QR code.`, "success");

            // Don't clear the email if it was auto-populated
            if (!currentEmployeeEmail) {
              document.getElementById("recipientEmail").value = "";
            }
          } else {
            showAlert("❌ Email sending failed: " + (data.error || "Unknown error"), "danger");
          }

        } catch (err) {
          console.error("Send email error:", err);

          // Provide helpful error messages
          let errorMessage = err.message;
          if (errorMessage.includes("401")) {
            errorMessage = "❌ Authentication required. Please sign in with Google or configure SMTP settings.";
          } else if (errorMessage.includes("400")) {
            errorMessage = "❌ Email configuration error. Please check your settings.";
          }

          showAlert("❌ Failed to send email: " + errorMessage, "danger");
        } finally {
          btn.innerHTML = originalText;
          btn.disabled = false;
        }
      }

      // Manual Employee Selection for Testing
      function selectEmployeeManually() {
        const employeeId = document.getElementById("manualEmployeeId").value.trim();

        if (!employeeId) {
          showAlert("Please enter an employee ID", "warning");
          return;
        }

        showAlert(`🔍 Looking up employee ID: ${employeeId}...`, "info");

        // Use the new direct employee lookup endpoint
        fetch("/get_employee_by_id", {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({ employee_id: employeeId }),
        })
          .then((res) => res.json())
          .then((data) => {
            if (data.error) {
              showAlert(`❌ ${data.error}`, "danger");
              return;
            }

            // Update display fields
            ["ID", "Name", "Position", "Company"].forEach((field) => {
              document.getElementById(field.toLowerCase()).textContent = data[field];
            });

            // Store current employee data
            currentEmployeeId = data.ID;
            currentEmployeeEmail = data.Email || null;

            // Auto-populate email if available
            const emailInput = document.getElementById("recipientEmail");
            if (emailInput) {
              if (currentEmployeeEmail) {
                emailInput.value = currentEmployeeEmail;
                emailInput.placeholder = "Email auto-populated from dataset";
                emailInput.style.backgroundColor = "#e8f5e8"; // Light green background
              } else {
                emailInput.value = "";
                emailInput.placeholder = "Enter email to send QR code";
                emailInput.style.backgroundColor = "#fff3cd"; // Light yellow background
              }
            }

            // Show appropriate email section
            if (emailAvailable) {
              const emailSection = document.getElementById("emailSection");
              emailSection.style.display = "block";

              // Scroll to email section for better visibility
              emailSection.scrollIntoView({ behavior: 'smooth', block: 'nearest' });

              // Highlight the email input briefly
              if (emailInput) {
                emailInput.focus();
                setTimeout(() => {
                  emailInput.style.boxShadow = "0 0 10px rgba(111, 66, 193, 0.5)";
                  setTimeout(() => {
                    emailInput.style.boxShadow = "";
                  }, 2000);
                }, 100);
              }
            } else {
              const noEmailSection = document.getElementById("noEmailSection");
              if (noEmailSection) {
                noEmailSection.style.display = "block";
              }
            }

            showAlert(`✅ Employee ${data.Name} (ID: ${data.ID}) selected successfully!${currentEmployeeEmail ? ` - Email: ${currentEmployeeEmail}` : ' - No email on file'}`, "success");
          })
          .catch((err) => {
            showAlert(`❌ Error looking up employee: ${err.message}`, "danger");
          });
      }

      // Manual Employee Selection for Main Interface
      function selectEmployeeManuallyMain() {
        const employeeId = document.getElementById("manualEmployeeIdMain").value.trim();

        if (!employeeId) {
          showAlert("Please enter an employee ID", "warning");
          return;
        }

        showAlert(`🔍 Looking up employee ID: ${employeeId}...`, "info");

        // Use the new direct employee lookup endpoint
        fetch("/get_employee_by_id", {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({ employee_id: employeeId }),
        })
          .then((res) => res.json())
          .then((data) => {
            if (data.error) {
              showAlert(`❌ ${data.error}`, "danger");
              return;
            }

            // Update display fields
            ["ID", "Name", "Position", "Company"].forEach((field) => {
              document.getElementById(field.toLowerCase()).textContent = data[field];
            });

            // Store current employee data
            currentEmployeeId = data.ID;
            currentEmployeeEmail = data.Email || null;

            // Auto-populate email if available
            const emailInput = document.getElementById("recipientEmail");
            if (emailInput) {
              if (currentEmployeeEmail) {
                emailInput.value = currentEmployeeEmail;
                emailInput.placeholder = "Email auto-populated from dataset";
                emailInput.style.backgroundColor = "#e8f5e8"; // Light green background
              } else {
                emailInput.value = "";
                emailInput.placeholder = "Enter email to send QR code";
                emailInput.style.backgroundColor = "#fff3cd"; // Light yellow background
              }
            }

            // Show appropriate email section
            if (emailAvailable) {
              const emailSection = document.getElementById("emailSection");
              if (emailSection) {
                emailSection.style.display = "block";

                // Scroll to email section for better visibility
                emailSection.scrollIntoView({ behavior: 'smooth', block: 'nearest' });

                // Highlight the email input briefly
                if (emailInput) {
                  emailInput.focus();
                  setTimeout(() => {
                    emailInput.style.boxShadow = "0 0 10px rgba(111, 66, 193, 0.5)";
                    setTimeout(() => {
                      emailInput.style.boxShadow = "";
                    }, 2000);
                  }, 100);
                }
              }
            }

            showAlert(`✅ Employee ${data.Name} (ID: ${data.ID}) selected successfully!${currentEmployeeEmail ? ` - Email: ${currentEmployeeEmail}` : ' - No email on file'}`, "success");

            // Clear the input after successful selection
            document.getElementById("manualEmployeeIdMain").value = "";
          })
          .catch((err) => {
            showAlert(`❌ Error looking up employee: ${err.message}`, "danger");
          });
      }

      // Auto-lookup employee when ID is entered (for streamlined interface)
      let lookupTimeout;
      function autoLookupEmployee() {
        const employeeId = document.getElementById("manualEmployeeIdMain").value.trim();
        const emailInput = document.getElementById("recipientEmailMain");
        const sendBtn = document.getElementById("sendEmailMainBtn");
        const employeeInfo = document.getElementById("employeeInfoMain");

        // Clear previous timeout
        clearTimeout(lookupTimeout);

        // Reset UI
        if (emailInput) {
          emailInput.value = "";
          emailInput.placeholder = "Email will auto-populate";
          emailInput.style.backgroundColor = "";
        }
        if (sendBtn) sendBtn.disabled = true;
        if (employeeInfo) employeeInfo.style.display = "none";

        if (!employeeId) {
          return;
        }

        // Debounce the lookup to avoid too many requests
        lookupTimeout = setTimeout(() => {
          fetch("/get_employee_by_id", {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify({ employee_id: employeeId }),
          })
            .then((response) => response.json())
            .then((data) => {
              if (data.error) {
                if (emailInput) {
                  emailInput.placeholder = "Employee not found";
                  emailInput.style.backgroundColor = "#f8d7da"; // Light red
                }
                return;
              }

              // Store current employee data
              currentEmployeeId = data.ID;
              currentEmployeeEmail = data.Email || null;

              // Update employee info display
              if (employeeInfo) {
                const nameEl = document.getElementById("employeeNameMain");
                const positionEl = document.getElementById("employeePositionMain");
                if (nameEl) nameEl.textContent = `${data.Name} (ID: ${data.ID})`;
                if (positionEl) positionEl.textContent = `${data.Position} - ${data.Company}`;
                employeeInfo.style.display = "block";
              }

              // Auto-populate email if available
              if (emailInput) {
                if (currentEmployeeEmail) {
                  emailInput.value = currentEmployeeEmail;
                  emailInput.placeholder = "Email from dataset (editable)";
                  emailInput.style.backgroundColor = "#d4edda"; // Light green
                  if (sendBtn) sendBtn.disabled = false;
                } else {
                  emailInput.value = "";
                  emailInput.placeholder = "Enter email address";
                  emailInput.style.backgroundColor = "#fff3cd"; // Light yellow
                  // Enable button anyway - user can enter email manually
                  if (sendBtn) sendBtn.disabled = false;
                }
              }
            })
            .catch((err) => {
              if (emailInput) {
                emailInput.placeholder = "Error looking up employee";
                emailInput.style.backgroundColor = "#f8d7da"; // Light red
              }
            });
        }, 500); // 500ms delay
      }

      // Send QR Email from main interface - Enhanced with Progress
      async function sendQREmailMain() {
        const employeeId = document.getElementById("manualEmployeeIdMain").value.trim();
        const emailInput = document.getElementById("recipientEmailMain");
        const btn = document.getElementById("sendEmailMainBtn");
        const progressDiv = document.getElementById("emailProgressMain");
        const progressBar = progressDiv.querySelector(".progress-bar");
        const statusText = document.getElementById("emailStatusMain");

        if (!emailInput || !btn) return;

        const email = emailInput.value.trim();

        if (!employeeId) {
          showAlert("Please enter an employee ID", "warning");
          return;
        }

        if (!email) {
          showAlert("Please enter an email address", "warning");
          return;
        }

        if (!email.includes('@')) {
          showAlert("Please enter a valid email address", "warning");
          return;
        }

        const originalText = btn.innerHTML;
        btn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status"></span> Sending...';
        btn.disabled = true;

        // Show progress
        progressDiv.style.display = "block";
        progressBar.style.width = "10%";
        statusText.textContent = "Preparing to send QR code email...";

        showAlert("📤 Sending QR code email...", "info");

        try {
          progressBar.style.width = "30%";
          statusText.textContent = "Connecting to email server...";

          const response = await fetch("/send_qr_email", {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify({
              employee_id: employeeId,
              email: email,
            }),
          });

          progressBar.style.width = "70%";
          statusText.textContent = "Processing email...";

          const data = await response.json();
          progressBar.style.width = "100%";

          if (data.success) {
            statusText.textContent = "Email sent successfully!";
            showAlert(`✅ QR code sent successfully to ${email}!`, "success");

            // Clear the form after successful send
            document.getElementById("manualEmployeeIdMain").value = "";
            emailInput.value = "";
            emailInput.style.backgroundColor = "";
            const employeeInfo = document.getElementById("employeeInfoMain");
            if (employeeInfo) employeeInfo.style.display = "none";
            btn.disabled = true;
          } else {
            statusText.textContent = "Failed to send email";
            progressBar.classList.add("bg-danger");
            showAlert(`❌ Failed to send email: ${data.error}`, "danger");
          }
        } catch (error) {
          progressBar.style.width = "100%";
          progressBar.classList.add("bg-danger");
          statusText.textContent = "Error occurred during email send";
          showAlert(`❌ Error sending email: ${error.message}`, "danger");
          console.error("Email send error:", error);
        } finally {
          btn.innerHTML = originalText;
          btn.disabled = false;

          // Hide progress after 3 seconds
          setTimeout(() => {
            progressDiv.style.display = "none";
            progressBar.style.width = "0%";
            progressBar.classList.remove("bg-danger");
          }, 3000);
        }
      }

      // Validate email input and enable/disable send button
      function validateEmailInput() {
        const emailInput = document.getElementById("recipientEmailMain");
        const sendBtn = document.getElementById("sendEmailMainBtn");
        const employeeId = document.getElementById("manualEmployeeIdMain").value.trim();

        if (!emailInput || !sendBtn) return;

        const email = emailInput.value.trim();
        const isValidEmail = email && email.includes('@') && email.includes('.');
        const hasEmployeeId = employeeId.length > 0;

        sendBtn.disabled = !(isValidEmail && hasEmployeeId);
      }

      // Enhanced Camera Functions
      // Note: html5QrCode and scannerActive are already declared above

      async function startScanning() {
        if (scannerActive) return;

        try {
          // Get available cameras
          availableCameras = await Html5Qrcode.getCameras();

          if (!availableCameras || availableCameras.length === 0) {
            showAlert("No cameras found on this device", "danger");
            return;
          }

          // Use current camera or default to first available
          const cameraId = currentCameraId || availableCameras[0].id;
          currentCameraId = cameraId;

          scannerActive = true;
          document.getElementById("start-button").style.display = "none";
          document.getElementById("stop-button").style.display = "inline-block";

          // Show switch camera button if multiple cameras available
          if (availableCameras.length > 1) {
            document.getElementById("switch-camera").style.display = "inline-block";
          }

          await html5QrCode.start(
            cameraId,
            {
              fps: 10,
              qrbox: { width: Math.min(250, window.innerWidth * 0.7), height: Math.min(250, window.innerWidth * 0.7) },
              aspectRatio: 1.0,
            },
            (decodedText, decodedResult) => {
              fetchData(decodedText.trim());
            },
            (error) => {
              // Ignore frequent scanning errors
              if (!error.includes("NotFoundException")) {
                console.error("QR Scanner error:", error);
              }
            }
          );

          showAlert("📷 Camera started successfully", "success");

        } catch (err) {
          scannerActive = false;
          showAlert("Camera error: " + err.message, "danger");
          console.error("Camera start error:", err);
        }
      }

      async function stopScanning() {
        if (!scannerActive) return;

        try {
          await html5QrCode.stop();
          scannerActive = false;
          document.getElementById("start-button").style.display = "inline-block";
          document.getElementById("stop-button").style.display = "none";
          document.getElementById("switch-camera").style.display = "none";
          showAlert("📷 Camera stopped", "info");
        } catch (err) {
          console.error("Error stopping camera:", err);
        }
      }

      async function switchCamera() {
        if (!scannerActive || availableCameras.length <= 1) return;

        try {
          // Stop current camera
          await html5QrCode.stop();

          // Find next camera
          const currentIndex = availableCameras.findIndex(cam => cam.id === currentCameraId);
          const nextIndex = (currentIndex + 1) % availableCameras.length;
          currentCameraId = availableCameras[nextIndex].id;

          // Start with new camera
          await html5QrCode.start(
            currentCameraId,
            {
              fps: 10,
              qrbox: { width: Math.min(250, window.innerWidth * 0.7), height: Math.min(250, window.innerWidth * 0.7) },
              aspectRatio: 1.0,
            },
            (decodedText, decodedResult) => {
              fetchData(decodedText.trim());
            },
            (error) => {
              if (!error.includes("NotFoundException")) {
                console.error("QR Scanner error:", error);
              }
            }
          );

          showAlert(`📷 Switched to: ${availableCameras[nextIndex].label || 'Camera ' + (nextIndex + 1)}`, "info");

        } catch (err) {
          showAlert("Failed to switch camera: " + err.message, "danger");
          console.error("Camera switch error:", err);
        }
      }

      // Responsive QR box size
      function updateQRBoxSize() {
        const size = Math.min(250, window.innerWidth * 0.7, window.innerHeight * 0.4);
        return { width: size, height: size };
      }

      // Handle window resize for responsive design
      window.addEventListener('resize', () => {
        if (scannerActive) {
          // Restart scanner with new dimensions
          stopScanning().then(() => {
            setTimeout(startScanning, 500);
          });
        }
      });

      // Download QR Code function
      function downloadQRCode() {
        if (!currentEmployeeId) {
          showAlert("No employee selected for download", "warning");
          return;
        }

        const qrFilename = `${String(currentEmployeeId).padStart(3, '0')}.png`;
        const downloadUrl = `/download/${qrFilename}`;

        // Create a temporary link element and trigger download
        const link = document.createElement('a');
        link.href = downloadUrl;
        link.download = `QR_Code_${currentEmployeeId}.png`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        showAlert(`✅ QR code downloaded for Employee ID: ${currentEmployeeId}`, "success");
      }

      // Password visibility toggle function
      function togglePasswordVisibility() {
        console.log("togglePasswordVisibility called"); // Debug log

        const passwordInput = document.getElementById("senderPassword");
        const toggleIcon = document.getElementById("passwordToggleIcon");

        if (!passwordInput || !toggleIcon) {
          console.error("Password input or toggle icon not found");
          return;
        }

        if (passwordInput.type === "password") {
          passwordInput.type = "text";
          toggleIcon.className = "fas fa-eye-slash";
          console.log("Password shown");
        } else {
          passwordInput.type = "password";
          toggleIcon.className = "fas fa-eye";
          console.log("Password hidden");
        }
      }

      // Show email configuration status - now uses unified notification system
      function showEmailConfigStatus(message, type = "info") {
        // Use the unified notification system instead of separate status div
        showAlert(message, type);
      }

      // Check email configuration on page load - removed automatic notification to prevent overlapping messages

      // Developer helper functions (accessible from browser console)
      window.resetAppSettings = function() {
        localStorage.clear();
        location.reload();
      };

      window.showSettingsModal = function() {
        document.getElementById("settingsModal").style.display = "flex";
      };

      // Set dynamic background from template image
      const templateImg = document.getElementById("id-template-preview");
      templateImg.onload = function() {
        const canvas = document.createElement("canvas");
        const context = canvas.getContext("2d");
        canvas.width = 1;
        canvas.height = 1;
        context.drawImage(templateImg, 0, 0, 1, 1);
        const [r, g, b] = context.getImageData(0, 0, 1, 1).data;
        const color = `rgb(${r}, ${g}, ${b})`;
        document.getElementById("paperBoundary").style.background = `radial-gradient(circle, ${color}, #000)`;
      };

      // Global error handler for better debugging
      window.addEventListener('error', function(e) {
        console.error('JavaScript error:', e.error);
        if (typeof showAlert === 'function') {
          showAlert("❌ JavaScript error: " + e.message, "danger");
        }
      });

      // Unhandled promise rejection handler
      window.addEventListener('unhandledrejection', function(e) {
        console.error('Unhandled promise rejection:', e.reason);
        if (typeof showAlert === 'function') {
          showAlert("❌ Network error: " + e.reason, "danger");
        }
      });
    </script>
  </body>
</html>
